using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.TrayNotify;
namespace The_Dining_Philosophers
{
    public class cadvimg
    {
        public Rectangle rsrc;
        public Rectangle rdst;
        public Bitmap img;
        public int dir;
        public int dir2;

    }
    public partial class Form1 : Form
    {
        
        List<CAct> shb3an = new List<CAct>();
        List<CAct> lsoramain = new List<CAct>();
        List<cadvimg> diningbb = new List<cadvimg>();
        List<CActfork> lforks = new List<CActfork>();
        List<CActp> lp = new List<CActp>();
        Bitmap off;
        Timer tt = new Timer();
        int ct1 = 0;
        int globalct = 0;
        public Form1()
        {
            WindowState= FormWindowState.Maximized;
            InitializeComponent();
            this.Paint += Form1_Paint;
            tt.Tick += Tt_Tick;
            tt.Interval = 1;
            tt.Start();
        }
        private void Tt_Tick(object sender, EventArgs e)
        {

            DrawDubb(this.CreateGraphics());
        }
        private void Form1_Paint(object sender, PaintEventArgs e)
        {
            DrawDubb(e.Graphics);
        }

        private void label1_Click(object sender, EventArgs e)
        {

        }

        private void textBox2_TextChanged(object sender, EventArgs e)
        {

        }

        private void button3_Click(object sender, EventArgs e)
        {
            bool isAlpha = textBox1.Text.All(Char.IsLetter);
            bool isAlpha2 = textBox1.Text.All(Char.IsSymbol);
            bool isAlpha3 = textBox1.Text.All(Char.IsWhiteSpace);
            bool isAlpha4 = textBox1.Text.All(Char.IsControl);
            bool isAlpha5 = textBox1.Text.All(Char.IsPunctuation);
            if (isAlpha == false && isAlpha2 == false && isAlpha3 == false && isAlpha4 == false && isAlpha5 == false)
            {

                if (textBox1.Text != "")
                {



                    if (Int64.Parse(textBox1.Text) >= 0 && Int64.Parse(textBox1.Text) <= 4)
                    {

                        int whichp = Convert.ToInt32(textBox1.Text);

                        if (lp[whichp].howmanhy >= 1)
                        {
                            for (int k = 0; k < lp[whichp].forkpos.Count; k++)
                            {
                                lforks[lp[whichp].forkpos[k]].took = 0;
                                ct1--;
                                globalct--;
                                lp[whichp].howmanhy -= 1;
                            }
                        }

                    }
                    else
                    {
                        MessageBox.Show("please enter a valid number");
                    }
                }
                else
                {
                    MessageBox.Show("please enter a valid number");
                }

            }
            else
            {
                MessageBox.Show("please enter a number");
            }
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            off = new Bitmap(this.ClientSize.Width, this.ClientSize.Height);
            createbb();
            ///////sora el  dinner table

            CAct pnn = new CAct();
            pnn.X = ClientSize.Width / 10;
            pnn.Y = ClientSize.Height / 10;
            pnn.W = 600;
            pnn.H = 450;
            pnn.img = new Bitmap("dining1.png");
            lsoramain.Add(pnn);
            /////// create el forks
            //////fdownright
            CActfork pn = new CActfork();
            pn.X = pnn.X + 202;
            pn.Y = pnn.Y + 145;
            pn.W = 40;
            pn.H = 40;
            pn.forkpos = 0;
            pn.img = new Bitmap("fdownright1.jpg");
            pn.img.MakeTransparent(pn.img.GetPixel(0, 0));
            lforks.Add(pn);
            ////fdownleft
            pn = new CActfork();
            pn.X = pnn.X + 350;
            pn.Y = pnn.Y + 145;
            pn.W = 40;
            pn.H = 40;
            pn.forkpos = 1;
            pn.img = new Bitmap("fdownleft1.png");
            pn.img.MakeTransparent(pn.img.GetPixel(0, 0));
            lforks.Add(pn);
            ///fupleft
            pn = new CActfork();
            pn.X = pnn.X + 400;
            pn.Y = pnn.Y + 255;
            pn.W = 40;
            pn.H = 40;
            pn.forkpos = 2;
            pn.img = new Bitmap("fupleft.png");
            pn.img.MakeTransparent(pn.img.GetPixel(0, 0));
            lforks.Add(pn);
            /////fdown
            pn = new CActfork();
            pn.X = pnn.X + pnn.img.Width / 2 + 30;
            pn.Y = pnn.Y + 340;
            pn.W = 40;
            pn.H = 40;
            pn.forkpos = 3;
            pn.img = new Bitmap("fup1.png");
            pn.img.MakeTransparent(pn.img.GetPixel(0, 0));
            lforks.Add(pn);
            ///fupright
            pn = new CActfork();
            pn.X = pnn.X + 145;
            pn.Y = pnn.Y + 255;
            pn.W = 40;
            pn.H = 40;
            pn.forkpos = 4;
            pn.img = new Bitmap("fupright1.jpg");
            pn.img.MakeTransparent(pn.img.GetPixel(0, 0));
            lforks.Add(pn);
            for (int i = 0; i < 5; i++)
            {
                CActp pnnn = new CActp();
                pnnn.howmanhy = 0;
                lp.Add(pnnn);
            }
            /////////shb3an0
            pnn = new CAct();
            pnn.img = new Bitmap("ana_shbe3t.png");
            pnn.img.MakeTransparent(pnn.img.GetPixel(0, 0));
            pnn.X = lsoramain[0].X + 250;
            pnn.Y = lsoramain[0].Y + 20;
            pnn.W = 80;
            pnn.H = 80;
            shb3an.Add(pnn);
            //////////shb3an1
            pnn = new CAct();
            pnn.img = new Bitmap("ana_shbe3t.png");
            pnn.img.MakeTransparent(pnn.img.GetPixel(0, 0));
            pnn.X = lsoramain[0].X + lsoramain[0].img.Width + 10;
            pnn.Y = lsoramain[0].Y + 140;
            pnn.W = 80;
            pnn.H = 80;
            shb3an.Add(pnn);
            //////////shb3an2
            pnn = new CAct();
            pnn.img = new Bitmap("ana_shbe3t.png");
            pnn.img.MakeTransparent(pnn.img.GetPixel(0, 0));
            pnn.X = lsoramain[0].X + lsoramain[0].img.Width - 85;
            pnn.Y = lsoramain[0].Y + 365;
            pnn.W = 80;
            pnn.H = 80;
            shb3an.Add(pnn);
            //////////shb3an3
            pnn = new CAct();
            pnn.img = new Bitmap("ana_shbe3t.png");
            pnn.img.MakeTransparent(pnn.img.GetPixel(0, 0));
            pnn.X = lsoramain[0].X + lsoramain[0].img.Width - 400;
            pnn.Y = lsoramain[0].Y + 365;
            pnn.W = 80;
            pnn.H = 80;
            shb3an.Add(pnn);
            //////////shb3an4
            pnn = new CAct();
            pnn.img = new Bitmap("ana_shbe3t.png");
            pnn.img.MakeTransparent(pnn.img.GetPixel(0, 0));
            pnn.X = lsoramain[0].X + lsoramain[0].img.Width - 490;
            pnn.Y = lsoramain[0].Y + 140;
            pnn.W = 80;
            pnn.H = 80;
            shb3an.Add(pnn);
            DrawDubb(CreateGraphics());
        }
        private void textBox1_TextChanged(object sender, EventArgs e)
        {

        }
        private void button1_Click(object sender, EventArgs e)
        {
            bool isAlpha = textBox1.Text.All(Char.IsLetter);
            bool isAlpha2 = textBox1.Text.All(Char.IsSymbol);
            bool isAlpha3 = textBox1.Text.All(Char.IsWhiteSpace);
            bool isAlpha4 = textBox1.Text.All(Char.IsControl);
            bool isAlpha5 = textBox1.Text.All(Char.IsPunctuation);
            if (isAlpha == false && isAlpha2 == false && isAlpha3 == false && isAlpha4 == false && isAlpha5 == false)
            {

                if (textBox1.Text != "")
                {



                    if (Int64.Parse(textBox1.Text) >= 0 && Int64.Parse(textBox1.Text) <= 4)
                    {


                        if (globalct <= 3)
                        {


                            int whichp = Convert.ToInt32(textBox1.Text);
                            if (lp[whichp].howmanhy <= 1)
                            {
                                ct1 += 1;
                                for (int i = whichp; i < 5; i++)
                                {


                                    if (whichp == 0 && (lforks[0].took == 1 || lforks[1].took == 1) && lp[whichp].howmanhy == 0)
                                    {
                                        MessageBox.Show("no nearby forks");
                                        break;
                                    }

                                    if (whichp == 1 && (lforks[1].took == 1 || lforks[2].took == 1) && lp[whichp].howmanhy == 0)
                                    {
                                        MessageBox.Show("no nearby forks");
                                        break;
                                    }

                                    if (whichp == 2 && (lforks[2].took == 1 || lforks[3].took == 1) && lp[whichp].howmanhy == 0)
                                    {
                                        MessageBox.Show("no nearby forks");
                                        break;
                                    }

                                    if (whichp == 3 && (lforks[3].took == 1 || lforks[4].took == 1) && lp[whichp].howmanhy == 0)
                                    {
                                        MessageBox.Show("no nearby forks");
                                        break;
                                    }

                                    if (whichp == 4 && (lforks[4].took == 1 || lforks[0].took == 1) && lp[whichp].howmanhy == 0)
                                    {
                                        MessageBox.Show("no nearby forks");
                                        break;
                                    }
                                    if (lforks[i].took == 0)
                                    {
                                        lforks[i].whichp = whichp;
                                        lforks[i].took = 1;
                                        lp[whichp].howmanhy += 1;
                                        lp[whichp].forkpos.Add(i);
                                        globalct++;
                                        break;
                                    }
                                    if (whichp == 4 && lforks[4].took == 1)
                                    {
                                        if (lforks[0].took == 0)
                                        {
                                            lforks[0].whichp = whichp;
                                            lforks[0].took = 1;
                                            lp[whichp].howmanhy += 1;
                                            lp[whichp].forkpos.Add(0);
                                            globalct++;
                                            break;
                                        }
                                    }
                                }
                            }
                            else
                            {
                                MessageBox.Show("the philosopher is eating");
                            }
                        }
                        else
                        {
                            MessageBox.Show("no enough forks");
                        }
                    }


                    else
                    {
                        MessageBox.Show("please enter a valid number");
                    }
                }
                else
                {
                    MessageBox.Show("please enter a valid number");
                }

            }
            else
            {
                MessageBox.Show("please enter a number");
            }
        }
        private void button2_Click(object sender, EventArgs e)
        {

        }
        void DrawDubb(Graphics g)
        {
            Graphics g2 = Graphics.FromImage(off);
            DrawScen(g2);
            g.DrawImage(off, 0, 0);
        }
        void createbb()
        {
            cadvimg pnn = new cadvimg();
            pnn.img = new Bitmap("diningbb.bmp");
            pnn.rsrc = new Rectangle(0, 0, pnn.img.Width, pnn.img.Height);
            pnn.rdst = new Rectangle(0, 0, ClientSize.Width , ClientSize.Height);
            diningbb.Add(pnn);

        }
        void DrawScen(Graphics g2)
        {
            Graphics g = this.CreateGraphics();
           // g2.Clear(Color.Black);
            for (int i = 0; i < diningbb.Count; i++)
            {
                cadvimg ptv = diningbb[i];
                g.DrawImage(ptv.img, ptv.rdst, ptv.rsrc, GraphicsUnit.Pixel);
            }

            for (int i = 0; i < lsoramain.Count; i++)
            {

                g2.DrawImage(lsoramain[i].img, lsoramain[i].X, lsoramain[i].Y, lsoramain[i].W, lsoramain[i].H);
            }
            for (int i = 0; i < lforks.Count; i++)
            {
                if (lforks[i].took == 0)
                {

                    g2.DrawImage(lforks[i].img, lforks[i].X, lforks[i].Y, lforks[i].W, lforks[i].H);
                }
            }
            for (int i = 0; i < shb3an.Count; i++)
            {

                if (lp[i].howmanhy == 2)
                {

                    g2.DrawImage(shb3an[i].img, shb3an[i].X, shb3an[i].Y, shb3an[i].W, shb3an[i].H);

                }
            }
        }
        
        public class CAct
        {
            public int X, Y, W, H;
            public Bitmap img;
            public int show = 0;
        }
        public class CActfork
        {
            public int X, Y, W, H;
            public Bitmap img;
            public int took = 0;
            public int forkpos = -1;
            public int whichp = -1;
        }
        public class CActp
        {
            public int howmanhy = 0;
            public List<int> forkpos = new List<int>();
        }
    }
}
