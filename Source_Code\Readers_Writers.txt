using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
namespace readers_writers
{
    public class cadvimg
    {
        public Rectangle rsrc;
        public Rectangle rdst;
        public Bitmap img;
        public int dir;
        public int dir2;

    }
    public partial class Form1 : Form
    {
        Label label = new Label();
        Label label1 = new Label();
        Label label2 = new Label();
        Button b = new Button();
        Button b1 = new Button();
        Button b2 = new Button();
        Button b3 = new Button();
        Button b4 = new Button();
        TextBox t = new TextBox();
        TextBox t1 = new TextBox();
        TextBox t2 = new TextBox();
        TextBox t3 = new TextBox();
        Bitmap s, s1, s2, s3;
        int n1, n2, ctcreate = 0;
        List<bool> l = new List<bool>();
        List<bool> l1 = new List<bool>();
        bool f1 = false, f2 = false;
        int flagwritingnow=-1;
        int flagimg = -1;
        int flagimg2 = -1;
        
        Bitmap off;
        List<cadvimg> background = new List<cadvimg>();

        public Form1()
        {
            WindowState = FormWindowState.Maximized;
            Load += new EventHandler(Form1_Load);
            Paint += new PaintEventHandler(Form1_Paint);
           // BackColor = Color.Gold;
        }


       
        //1st page
        public void create()
        {
            label.Location = new Point(ClientSize.Width / 2+100, ClientSize.Height / 2 - 200);
            label.Size = new Size(460, 60);//350,40
            label.Text = "How many viewers!";
            label.Font = new Font("Arial", 22, FontStyle.Bold);
            label.ForeColor = Color.Teal;
            label.TextAlign = ContentAlignment.MiddleCenter;
            Controls.Add(label);
            //
            label1.Location = new Point(ClientSize.Width / 2 - 600, ClientSize.Height / 2 - 200);
            label1.Size = new Size(460, 60);
            label1.Text = "How many writers!";
            label1.Font = new Font("Arial", 22, FontStyle.Bold);
            label1.ForeColor = Color.DarkBlue;
            label1.TextAlign = ContentAlignment.MiddleCenter;
            Controls.Add(label1);
            //
            t.Location = new Point(ClientSize.Width / 2 - 450, ClientSize.Height / 2 -50);
            t.Size = new Size(100, 30);
            t.Font = new Font("Arial", 22, FontStyle.Bold);
            t.ForeColor = Color.Black;
            t.BackColor = Color.CadetBlue;
            Controls.Add(t);
            //
            t3.Location = new Point(ClientSize.Width / 2 + 300, ClientSize.Height / 2 -50 );
            t3.Size = new Size(100, 30);
            t3.Font = new Font("Arial", 22, FontStyle.Bold);
            t3.ForeColor = Color.Black;
            t3.BackColor = Color.CadetBlue;
            Controls.Add(t3);
            //
            b.Location = new Point(ClientSize.Width / 2 - 150, ClientSize.Height / 2 +100);
            b.Size = new Size(300, 60);
            b.Text = "Present!";
            b.Font = new Font("Arial", 22, FontStyle.Bold);
            b.BackColor = Color.DarkBlue;
            b.ForeColor = Color.White;
            Controls.Add(b);
        }
        public void set_pos()
        {
            label2.Location = new Point(ClientSize.Width/2-200, ClientSize.Height / 2 + 100);
            label2.Size = new Size(400, 50);
            label2.Text = "Readers Counter";
            label2.Font = new Font("Arial", 22, FontStyle.Bold);
            label2.ForeColor = Color.White;
            label2.BackColor = Color.MediumSlateBlue;
            label2.TextAlign = ContentAlignment.MiddleCenter;
            //
            b1.Location = new Point(ClientSize.Width / 2 + 300, ClientSize.Height / 2 - 100);
            b1.Size = new Size(120, 40);
            b1.Text = "Read";
            b1.Font = new Font("Arial", 22, FontStyle.Bold);
            b1.BackColor = Color.Teal;
            b1.ForeColor = Color.White;
            //
            b2.Location = new Point(ClientSize.Width / 2 - 465, ClientSize.Height / 2 - 100);
            b2.Size = new Size(120, 40);
            b2.Text = "Write";
            b2.Font = new Font("Arial", 22, FontStyle.Bold);
            b2.BackColor = Color.Blue;
            b2.ForeColor = Color.White;
            //
            b3.Location = new Point(ClientSize.Width / 2 + 300, ClientSize.Height / 2-50 );
            b3.Size = new Size(120, 40);
            b3.Text = "Stop Read";
            b3.Font = new Font("Arial", 22, FontStyle.Bold);
            b3.BackColor = Color.Teal;
            b3.ForeColor = Color.White;
            //
            b4.Location = new Point(ClientSize.Width / 2 - 465, ClientSize.Height / 2-50 );
            b4.Size = new Size(120, 40);
            b4.Text = "Stop Write";
            b4.Font = new Font("Arial", 22, FontStyle.Bold);
            b4.BackColor = Color.Blue;
            b4.ForeColor = Color.White;
            //
            t1.Location = new Point(ClientSize.Width / 2 + 305, ClientSize.Height / 2 );
            t1.Size = new Size(100, 30);
            t1.Font = new Font("Arial", 14, FontStyle.Bold);
            t1.ForeColor = Color.Teal;
            t1.BackColor = Color.White;
            //
            t2.Location = new Point(ClientSize.Width / 2 - 460, ClientSize.Height / 2 );
            t2.Size = new Size(100, 30);
            t2.Font = new Font("Arial", 14, FontStyle.Bold);
            t2.ForeColor = Color.DarkBlue;
            t2.BackColor = Color.White;
        }
        public void initialize()
        {
            if (ctcreate == 0)
            {
                create();
                ctcreate = 1;
            }
            set_pos();
            b.Click += (sender, e) =>
            {
                if (int.TryParse(t.Text, out n2) && int.TryParse(t3.Text, out n1))
                {
                    for (int i = 0; i < n1; i++)
                        l.Add(false);
                    for (int i = 0; i < n2; i++)
                        l1.Add(false);
                    label.Text = "Readers! :" + n1;
                    label1.Text = "Writers! :" + n2;
                    label.ForeColor = Color.Teal;
                    label1.ForeColor = Color.Blue;
                    if (ctcreate == 1)
                    {
                        Controls.Remove(t3);
                        Controls.Remove(t);
                        Controls.Remove(b);
                        ctcreate++;
                    }
                    create1();
                }
                else
                    MessageBox.Show("Please enter a valid number");
            };
            int index, index1;
            b1.Click += (sender, e) =>
            {
                if (!f2)
                {
                    if (int.TryParse(t1.Text, out index))
                        if (index >= 0 && index < l.Count)
                            if (!l[index])
                            {
                                f1 = true;
                                f2 = false;
                                l[index] = true;
                            }
                    create2();
                }
            };
            b2.Click += (sender, e) =>
            {
                if (!f1 && !f2)
                {
                    if (int.TryParse(t2.Text, out index1))
                        if (index1 >= 0 && index1 < l1.Count)
                            if (!l1[index1])
                            {
                                f2 = true;
                                l1[index1] = true;
                            }
                    create2();
                }
            };
            b3.Click += (sender, e) =>
            {
                if (!f2)
                {
                    if (int.TryParse(t1.Text, out index))
                        if (index >= 0 && index < l.Count)
                        {
                            if (l[index])
                                l[index] = false;
                            f1 = false;
                            for (int i = 0; i < l.Count; i++)
                                if (l[i])
                                {
                                    f1 = true;
                                    break;
                                }
                        }
                    create2();
                }
            };
            b4.Click += (sender, e) =>
            {
                if (!f1 && f2)
                {
                    if (int.TryParse(t2.Text, out index1))
                        if (index1 >= 0 && index1 < l1.Count)
                            if (l1[index1])
                            {
                                f2 = false;
                                l1[index1] = false;
                            }
                    create2();
                }
            };
        }
        private void Form1_Paint(object sender, PaintEventArgs e)
        {

           
            s = new Bitmap("read.bmp");
           
            s1 = new Bitmap("write.bmp");
      
            s2 = new Bitmap("read.bmp");

            s3 = new Bitmap("write.bmp");

            drawscene(CreateGraphics());
        }

        public void create1()
        {
            Controls.Add(t1);
            Controls.Add(t2);
            Controls.Add(b1);
            Controls.Add(b2);
            Controls.Add(b3);
            Controls.Add(b4);
           //  Graphics g = CreateGraphics();
            flagimg = 1;
        }
        public void create2()
        {
            int ct = 0;
            //Graphics g = CreateGraphics();
            for (int i = 0; i < l.Count; i++)
                if (l[i])
                    ct++;
            if (ct > 0)
            {
                flagimg2 = 1;
                label2.Text = "Reading now: " + ct;
                label.Text = "Readers :" + (n1 - ct);
                Controls.Add(label2);
            }
            else
            {
               // g.Clear(Color.Gold);
                create1();
                Controls.Remove(label2);
                flagimg2 = -1;
                label.Text = "Readers :" + (n1 - ct);
            }
            int ct1 = 0;
            for (int i = 0; i < l1.Count; i++)
                if (l1[i])
                    ct1++;
            if (ct1 > 0)
            {
                flagwritingnow = 1;
                label2.Text = "Writing now : " + ct1;
                //
                //label2.Location = new Point(ClientSize.Width - 350, ClientSize.Height / 2 +500);
                //
                label1.Text = "Writers :" + (n2 - ct1);
                Controls.Add(label2);
            }
            else if (ct < 1)
            {
               // g.Clear(Color.Gold);
                create1();
                Controls.Remove(label2);
                flagwritingnow = -1;
                label1.Text = "Writers :" + (n2 - ct1);
            }
        }
        void drawscene(Graphics g)
        {
            for (int i = 0; i < background.Count; i++)
            {
                cadvimg ptv = background[i];
                g.DrawImage(ptv.img, ptv.rdst, ptv.rsrc, GraphicsUnit.Pixel);
            }

            if(flagwritingnow==1)
            {
                g.DrawImage(s2, ClientSize.Width/2-50, ClientSize.Height / 2 + 150, 100, 150);
            }
           if(flagimg2==1)
           {
                g.DrawImage(s2, ClientSize.Width/2 - 50, ClientSize.Height / 2 + 150, 100, 150);
           }
           

          if(flagimg==1)
          {
                g.DrawImage(s, ClientSize.Width / 2 -150, ClientSize.Height / 2 - 300, 90, 90);//150
                g.DrawImage(s1, ClientSize.Width / 2 + 40, ClientSize.Height / 2 - 300, 90, 90);
          }
        }
        void createbb()
        {
            cadvimg pnn = new cadvimg();
            pnn.img = new Bitmap("bb.bmp");
            pnn.rsrc = new Rectangle(0, 0, pnn.img.Width, pnn.img.Height);
            pnn.rdst = new Rectangle(0, 0, ClientSize.Width , ClientSize.Height);
            background.Add(pnn);
        }
        private void Form1_Load(object sender, EventArgs e)
        {
           
            createbb();
           
            //BackColor = Color.Gold;
            initialize();
            off = new Bitmap(ClientSize.Width, ClientSize.Height);


            drawscene(CreateGraphics());
        }

       
    }
}
