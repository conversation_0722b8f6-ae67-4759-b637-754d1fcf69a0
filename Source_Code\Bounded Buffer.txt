using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace os_project
{

    public partial class Form1 : Form
    {
        Button b1 = new Button();
        Button b2 = new Button();
        Bitmap off;
        int num = 0;
        int flagstart = 0;
        int flagcreate = 0;
        int whichdata = 0;
        int numbuffer = 0;
        int flagdoitconsume = 0;
        int flagdoitproduce = 0;
        int ctick = 0;
        Timer tt = new Timer();
        CActmoraba3 pnn = new CActmoraba3();
        List<CActmoraba3> lmoraba3db = new List<CActmoraba3>();
        List<CActmoraba3> lmorba3fill = new List<CActmoraba3>();
        List<CActmoraba3> button = new List<CActmoraba3>();
        List<CAct> img = new List<CAct>();
        List<CAct> img2 = new List<CAct>();
        public Form1()
        {
            InitializeComponent();
            this.Paint += Form1_Paint;
            this.MouseDown += Form1_MouseDown;
            tt.Tick += Tt_Tick;
            tt.Interval = 1;
            tt.Start();
            b1.Click += B1_Click;
            b2.Click += B2_Click;
        }

        private void B2_Click(object sender, EventArgs e)
        {

            if (flagdoitproduce == 0&&flagdoitconsume==0)
            {


                if (whichdata > 0)
                {
                    if(img.Count>0)
                    {
                        img.Clear();
                    }
                    whichdata -= 1;
                    flagdoitconsume = 1;
                }
                else
                {
                    CAct pnn = new CAct();
                    pnn.img = new Bitmap("clock.png");
                    pnn.W = 100;
                    pnn.H = 100;
                    pnn.X = lmoraba3db[lmoraba3db.Count - 1].X + lmoraba3db[lmoraba3db.Count - 1].W;
                    pnn.Y = 2 * ClientSize.Height / 3;
                    img2.Add(pnn);
                }
            }

        }
       
        void animateconsume()
        {
            if (lmorba3fill[whichdata].Y <= lmoraba3db[whichdata].Y + lmoraba3db[whichdata].H / 2 - pnn.H / 2 + 120)
            {
                lmorba3fill[whichdata].Y += 10;
                CAct pnn = new CAct();
                pnn.img = new Bitmap("clock.png");
                pnn.W = 100;
                pnn.H = 100;
                pnn.X = 0 + lmoraba3db[lmoraba3db.Count - 1].W;
                pnn.Y = 2 * ClientSize.Height / 3;
                img2.Add(pnn);
                
            }
            else
            {
                img2.Clear();
                flagdoitconsume = 0;
                deleteputboxes();
                return;
            }
        }
        void deleteputboxes()
        {
            lmorba3fill.RemoveAt(whichdata);
        }
    
        private void B1_Click(object sender, EventArgs e)
        {
            ////producer
            if(flagdoitproduce==0 && flagdoitconsume == 0)
            {

            
            if (whichdata<numbuffer)
            {
                    if (img2.Count > 0)
                    {
                        img2.Clear();
                    }
                    pnn = new CActmoraba3();
                pnn.W = 50;
                pnn.H = 50;
                pnn.X = lmoraba3db[whichdata].X + lmoraba3db[whichdata].W / 2 - pnn.W / 2;
                pnn.Y = lmoraba3db[whichdata].Y + lmoraba3db[whichdata].H / 2 - pnn.H / 2+120;
                pnn.clr = Color.Purple;
                lmorba3fill.Add(pnn);              
                flagdoitproduce = 1;
            }
            else
            {
                CAct pnn = new CAct();
                pnn.img = new Bitmap("clock.png");
                pnn.W = 100;
                pnn.H = 100;
                pnn.X = 0 + lmoraba3db[lmoraba3db.Count - 1].W;
                pnn.Y = 2 * ClientSize.Height / 3;
                img2.Add(pnn);
            }
            }
        }

        private void Form1_MouseDown(object sender, MouseEventArgs e)
        {
         
        }     
        private void Tt_Tick(object sender, EventArgs e)
        {
            if(flagstart==1)
            {
                if (flagcreate == 0)
                {
                    createmoraba3();
                    flagcreate = 3;
                }
                if(flagdoitproduce==1&&ctick%10==0)
                {
                    moveproduce();
                }
                if (flagdoitconsume == 1 && ctick % 10 == 0)
                {
                    animateconsume();
                }
                ctick += 1;
                DrawDubb(this.CreateGraphics());
            }
        }
        void moveproduce()
        {
            if(lmorba3fill[whichdata].Y>= lmoraba3db[whichdata].Y + lmoraba3db[whichdata].H / 2 - pnn.H / 2+10)
            {
                lmorba3fill[whichdata].Y -= 10;
                CAct pnn = new CAct();
                pnn.img = new Bitmap("clock.png");
                pnn.W = 100;
                pnn.H = 100;
                pnn.X = lmoraba3db[lmoraba3db.Count - 1].X + lmoraba3db[lmoraba3db.Count - 1].W;
                pnn.Y = 2 * ClientSize.Height / 3;
                img.Add(pnn);
            }
            else
            {
                img.Clear();
                flagdoitproduce = 0;
                whichdata += 1;
                return;
            }
        }

        private void Form1_Paint(object sender, PaintEventArgs e)
        {
            DrawDubb(e.Graphics);
        }

        private void label2_Click(object sender, EventArgs e)
        {

        }

        private void textBox1_TextChanged(object sender, EventArgs e)
        {

        }

        private void button1_Click(object sender, EventArgs e)
        {
            bool isAlpha = textBox1.Text.All(Char.IsLetter);
            bool isAlpha2 = textBox1.Text.All(Char.IsSymbol);
            bool isAlpha3 = textBox1.Text.All(Char.IsWhiteSpace);
            bool isAlpha4 = textBox1.Text.All(Char.IsControl);
            bool isAlpha5 = textBox1.Text.All(Char.IsPunctuation);
            if (isAlpha==false&& isAlpha2==false&& isAlpha3 == false && isAlpha4 == false && isAlpha5 == false)
            {

                if (textBox1.Text != "")
                {
                    if (textBox1.Text != "1")
                    {
                        if (Int64.Parse(textBox1.Text) > 0)
                        {
                            num = Convert.ToInt32(textBox1.Text);
                            numbuffer = num;
                            textBox1.Hide();
                            button1.Hide();
                            label1.Hide();
                            label2.Hide();
                            flagstart = 1;
                        }
                        else
                        {
                            MessageBox.Show("please enter a valid number");
                        }
                    }
                    else
                    {
                        MessageBox.Show("please enter a valid number");
                    }

                }
                else
                {
                    MessageBox.Show("please enter a number");
                }
            }
            else
            {
                MessageBox.Show("please enter a number");
            }
        }
        void createmoraba3()
        {
            ////////create cells
            CActmoraba3 pnn = new CActmoraba3();
            pnn.X = 0;
            pnn.Y = ClientSize.Height / 3;
            pnn.W = 100;
            pnn.H = 100;
            pnn.clr = Color.Blue;
            lmoraba3db.Add(pnn);
            for (int i=1;i<num;i++)
            {
                 pnn = new CActmoraba3();
                pnn.X = lmoraba3db[i - 1].X + lmoraba3db[i - 1].W+10;
                pnn.Y = ClientSize.Height / 3;
                pnn.W = 100;
                pnn.H = 100;
                pnn.clr = Color.Blue;
                lmoraba3db.Add(pnn);
            }
            /////////////create 2 buttons                  
            

            b1.Text = "Producer";
            b1.Location = new Point(0, 2 * ClientSize.Height / 3);
            b1.Size = new Size(100,100);
            b1.ForeColor = Color.Black;
            b1.BackColor = Color.Orange;
            this.Controls.Add(b1);
            ////
            b2.Text = "Consumer";
            b2.Location = new Point(lmoraba3db[lmoraba3db.Count-1].X, 2 * ClientSize.Height / 3);
            b2.Size = new Size(100, 100);
            b2.ForeColor = Color.Black;
            b2.BackColor = Color.Orange;
            this.Controls.Add(b2);

        }
        void DrawDubb(Graphics g)
        {
            Graphics g2 = Graphics.FromImage(off);
            DrawScen(g2);
            g.DrawImage(off, 0, 0);
        }

        void DrawScen(Graphics g2)
        {
            Graphics g = this.CreateGraphics();
            g2.Clear(Color.White);
            for (int i = 0; i < lmoraba3db.Count; i++)
            {
                SolidBrush Pn = new SolidBrush(lmoraba3db[i].clr);
                g2.FillRectangle(Pn, lmoraba3db[i].X, lmoraba3db[i].Y, lmoraba3db[i].W, lmoraba3db[i].H);
            }
            for (int i = 0; i < button.Count; i++)
            {
                SolidBrush Pn = new SolidBrush(button[i].clr);
                g2.FillRectangle(Pn, button[i].X, button[i].Y, button[i].W, button[i].H);
            }
            for (int i = 0; i < lmorba3fill.Count; i++)
            {
                SolidBrush Pn = new SolidBrush(lmorba3fill[i].clr);
                g2.FillRectangle(Pn, lmorba3fill[i].X, lmorba3fill[i].Y, lmorba3fill[i].W, lmorba3fill[i].H);
            }
            for (int i = 0; i < img.Count; i++)
            {

                g2.DrawImage(img[i].img, img[i].X, img[i].Y, img[i].W,img[i].H);
            }
            for (int i = 0; i < img2.Count; i++)
            {

                g2.DrawImage(img2[i].img, img2[i].X, img2[i].Y, img2[i].W, img2[i].H);
            }
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            off = new Bitmap(this.ClientSize.Width, this.ClientSize.Height);
        }

        private void label1_Click(object sender, EventArgs e)
        {

        }
    }
    public class CActmoraba3
    {
        public int X, Y, W, H;
        public Color clr;
        public string Letter;
    }
    public class CAct
    {
        public int X, Y,W,H;
        public Bitmap img;
    }
}
