﻿using System;
using System.Windows.Forms;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Runtime.InteropServices;

namespace Elmenu

{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
            WindowState=FormWindowState.Maximized;
           
        }

        //
        [DllImport("user32.dll")]
        public static extern int SendMessage(IntPtr hWnd, int Msg, int wParam, int lParam);
        [DllImport("user32.dll")]
        public static extern bool ReleaseCapture();

        public const int WM_NCLBUTTONDOWN = 0xA1;
        public const int HT_CAPTION = 0x2;
        //

        private void SetRoundedRegion()
        {
            System.Drawing.Drawing2D.GraphicsPath path = new System.Drawing.Drawing2D.GraphicsPath();
            int radius = 30;
            
            int width = this.Width;
            int height = this.Height;

            
           
            
            path.AddArc(0, 0, radius, radius, 180, 90);
            path.AddArc(width - radius, 0, radius, radius, 270, 90);
            path.AddArc(width - radius, height - radius, radius, radius, 0, 90);
            path.AddArc(0, height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();

          
            this.Region = new Region(path);
        }

        private void button1_Click(object sender, EventArgs e)
        {
            Process.Start(@"run_readers.bat");
        }
        private void button2_Click(object sender, EventArgs e)
        {
            Process.Start(@"dining.bat");
        }
        private void button3_Click(object sender, EventArgs e)
        {
            Process.Start(@"bounded.bat");
        }
        private void Form1_Load(object sender, EventArgs e)
        {
            SetRoundedRegion();
        }

        private void label1_Click(object sender, EventArgs e)
        {

        }

        private void button4_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void panel1_MouseDown(object sender, MouseEventArgs e)
        {
            if(e.Button == MouseButtons.Left)
            {
                if(e.Clicks==1&&e.Y<=Height&&e.Y>=0)
                {
                    ReleaseCapture();
                    SendMessage(this.Handle, WM_NCLBUTTONDOWN, HT_CAPTION,0);
                }
            }
        }
    }
}